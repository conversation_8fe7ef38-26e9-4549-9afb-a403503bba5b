---
- name: "Update monitoring configuration files"
  ansible.builtin.include_tasks: task-update_single_config.yml
  loop:
    - config_file: "node-exporter.yml"
      relative_dir: "{{ relative_prometheus_config_dir }}"
      service_to_restart: "prometheus"
    - config_file: "mongodb-exporter.yml"
      relative_dir: "{{ relative_prometheus_config_dir }}"
      service_to_restart: "prometheus"
    - config_file: "redis-monitor.conf"
      relative_dir: "{{ relative_redis_monitor_config_dir }}"
      service_to_restart: "redis"
    - config_file: "loki-config.yaml"
      relative_dir: "{{ relative_loki_config_dir }}"
      service_to_restart: "loki"
    - config_file: "promtail-config.yaml"
      relative_dir: "{{ relative_loki_config_dir }}"
      service_to_restart: "promtail"
    - config_file: "grafana.ini"
      relative_dir: "{{ relative_grafana_config_dir }}"
      service_to_restart: "grafana"
  loop_control:
    loop_var: config_item
