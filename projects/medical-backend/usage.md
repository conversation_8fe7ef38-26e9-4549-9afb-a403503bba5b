# Medical Backend Playbook 使用指南

本文档为 Medical Backend Ansible Playbook 提供全面的使用说明。

## 概述

医疗后端剧本提供四个主要功能：
1. **脚本执行** - 在目标主机上执行 shell 脚本
2. **监控服务** - 升级和配置 Grafana 监控堆栈
3. **数据库操作** - MongoDB 认证更新
4. **服务重启** - 重启医疗后端服务

## 1. 脚本执行

在医疗后端基础设施上执行 shell 脚本。

### 必需变量
- `script_name`: `roles/01-common/files/` 中的脚本文件名
- `target_group`: 要执行的清单组名称
- `release_tag`: 脚本的发布标签
- `remote_scripts_dir`: 复制和执行脚本的远程目录
- `sync_base_shells`: 是否将基础 shell 同步到远程主机（默认：true）

### 可选变量
- `execution_timeout`: 脚本超时时间（秒）（默认：-1，无超时）

### 示例
```bash
# 执行 Kenta 后端脚本
ansible-playbook site.yml -t execute_shell -e "@extra-vars/kenta.backend.yml"

# 执行带超时的 Bureau 后端脚本
ansible-playbook site.yml -t execute_shell -e "@extra-vars/bureau.backend.yml" \
  --extra-vars "MEDICAL.scripts.execution_timeout=600"

# 执行自定义脚本
ansible-playbook site.yml -t execute_shell \
  -e "target_group=medical_servers script_name=custom.sh release_tag=v1.0.0 remote_scripts_dir=/tmp/scripts"
```

## 2. 监控服务 (Grafana 堆栈)

升级监控服务并更新配置。

### 必需变量
- `grafana_image_name`: grafana/grafana
- `grafana_version`: Grafana 版本
- `loki_image_name`: grafana/loki
- `loki_version`: Loki 版本
- `promtail_image_name`: grafana/promtail
- `promtail_version`: Promtail 版本
- `prometheus_image_name`: prom/prometheus
- `prometheus_version`: Prometheus 版本
- `node_exporter_image_name`: prom/node-exporter
- `node_exporter_version`: Node exporter 版本
- `sync_docker_compose`: 是否将 docker-compose.yml 同步到远程主机（默认：true）
- `relative_prometheus_config_dir`: prometheus/config（docker-compose.yml 根路径后的子路径）
- `relative_redis_monitor_config_dir`: service/redis/conf（docker-compose.yml 根路径后的子路径）
- `relative_loki_config_dir`: loki/config（docker-compose.yml 根路径后的子路径）
- `relative_grafana_config_dir`: grafana（docker-compose.yml 根路径后的子路径）

### 示例
```bash
# 升级所有监控服务
ansible-playbook site.yml -t medical_grafana -e "@extra-vars/monitor.yml"

# 仅升级服务
ansible-playbook site.yml -t upgrade_services -e "@extra-vars/monitor.yml"

# 仅更新配置
ansible-playbook site.yml -t update_configs -e "@extra-vars/monitor.yml"
```

## 3. MongoDB 认证更新

在所有医疗后端服务中更新 MongoDB 认证字符串。

### 必需变量
- `old_string`: 配置文件中要替换的字符串
- `new_string`: 要替换成的新字符串

### 受影响的服务
- 贤太API集群 (***********)
- 事务局 (***********)
- 贤太识别端 (172.17.0.202)
- Smart药局 (***********)
- GreenMedic (172.17.0.8, 172.17.0.37)
- selector环境 (***********)
- neox-inc官网 (*************)
- smart药局统计执行文件相关 (***********)

### 示例
```bash
# 使用预定义变量更新
ansible-playbook site.yml -t mongo_auth_update -e "@extra-vars/mongo.auth.update.yml"

# 使用内联变量更新
ansible-playbook site.yml -t mongo_auth_update \
  -e "old_string='yakumaru:oldpassword' new_string='yakumaru:newpassword'"

# 更新 MongoDB 连接字符串
ansible-playbook site.yml -t medical_database \
  -e "old_string='***********************' new_string='***********************'"
```

## 4. 服务重启

强大且幂等的服务重启功能，支持基于组和单个服务的操作。

### 必需变量（至少一个）
- `restart_groups`: 要重启的服务组列表
- `restart_services`: 要重启的单个服务列表

### 可用服务组
- **recognize** (识别端): async-merge, async-dispatcher, med-queue, async-recognize
- **qps** (QPS): selector
- **api** (API集群): php-fpm-74, nginx, med-queue
- **bureau** (事务局端): bureau-queue, med-cover-original, prescription
- **smart** (Smart药局): smart-queue

### 可用单个服务
- async-merge, async-dispatcher, med-queue, async-recognize
- selector, php-fpm-74, nginx, bureau-queue
- med-cover-original, prescription, smart-queue

### 配置选项
可以在 `roles/04-restart-services/vars/main.yml` 中覆盖配置：
- `remote_logs_dir`: 日志目录（默认：/mnt/efs/production/devops/logs/ansible-service-restart-logs）
- `execution_timeout`: 超时时间（秒），-1 表示无超时（默认：-1）
- `max_retry_attempts`: 重试次数（默认：3）
- `retry_delay`: 重试间隔时间（秒）（默认：10）

### 示例

#### 基本服务重启
```bash
# 按组重启服务（支持多种格式）
ansible-playbook site.yml -t restart_services -e "restart_groups=['recognize']"
ansible-playbook site.yml -t restart_services -e '{"restart_groups":["recognize","api"]}'
ansible-playbook site.yml -t restart_services -e "restart_groups: [bureau]"

# 重启单个服务（支持多种格式）
ansible-playbook site.yml -t restart_services -e "restart_services=['nginx','php-fpm-74']"
ansible-playbook site.yml -t restart_services -e '{"restart_services":["med-cover-original","prescription"]}'
ansible-playbook site.yml -t restart_services -e "restart_services: [async-merge]"

# 混合重启（组 + 单个服务）
ansible-playbook site.yml -t restart_services \
  -e "restart_groups=['qps'] restart_services=['nginx','bureau-queue']"
```

#### 事务局服务示例
```bash
# 重启事务局服务（med-cover-original 和 prescription）
ansible-playbook site.yml -t restart_services -e "restart_services=['med-cover-original','prescription']"
ansible-playbook site.yml -t restart_services -e "restart_groups=['bureau']"

# 重启单个事务局服务
ansible-playbook site.yml -t restart_services -e "restart_services=['med-cover-original']"
```

#### 高级配置
```bash
# 使用自定义超时配置
ansible-playbook site.yml -t restart_services -e "restart_groups=['api']" \
  --extra-vars "services_info.restart.defaults.execution_timeout=300"

# 使用详细输出进行调试
ansible-playbook site.yml -t restart_services -e "restart_services=['nginx']" -vvv
```

### 功能特性
- 强大的错误处理和重试机制
- 重启前后状态验证
- 全面的日志记录和状态跟踪
- 幂等操作（可安全地多次运行）
- 部分失败支持（继续处理其他服务）
- 自动日志清理（保留最近 20 次执行）

### 日志文件
位置：`/mnt/efs/production/devops/logs/ansible-service-restart-logs/`
- `service-restart-execution.log`: 主执行日志
- `service-restart-{service}-{timestamp}.log`: 单个重启日志（包含状态验证）

## 常用标签

- `execute_shell`: 执行 shell 脚本
- `medical_backend`: 所有医疗后端操作
- `medical_grafana`: Grafana 监控操作
- `monitor`: 监控服务
- `medical_database`: 数据库操作
- `mongo_auth_update`: MongoDB 认证更新
- `database`: 数据库操作
- `restart_services`: 服务重启操作

## 清单组

- `medical_servers`: 主要医疗后端服务器 (***********)
- `recognize_servers`: 识别服务器 (172.17.0.202)
- `grafana_servers`: 监控服务器 (172.17.0.241)
- `homepage_servers`: 主页服务器 (*************)

## 额外变量文件

在 `extra-vars/` 目录中提供了预配置的变量文件：
- `kenta.backend.yml`: Kenta 后端脚本执行
- `kenta.recognize.yml`: Kenta 识别脚本执行
- `bureau.backend.yml`: Bureau 后端脚本执行
- `bureau.frontend.yml`: Bureau 前端脚本执行
- `smart.backend.yml`: Smart 后端脚本执行
- `smart.frontend.yml`: Smart 前端脚本执行
- `homepage.app.yml`: 主页应用脚本执行
- `yakumaru-hp.app.yml`: Yakumaru 主页脚本执行
- `monitor.yml`: 监控服务配置
- `mongo.auth.update.yml`: MongoDB 认证更新配置
